/* 统一页面布局和样式 */

/* 重置样式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: "微软雅黑", Arial, Helvetica, sans-serif;
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  background-color: #f8f8f8;
}

/* 容器样式 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 行和列的统一布局 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.row:after {
  content: "";
  display: table;
  clear: both;
}

[class*='col-'] {
  position: relative;
  padding-left: 15px;
  padding-right: 15px;
  min-height: 1px;
  box-sizing: border-box;
}

.col-md-3 {
  width: 25%;
}

.col-md-4 {
  width: 33.33333%;
}

.col-md-6 {
  width: 50%;
}

.col-md-8 {
  width: 66.66666%;
}

.col-md-9 {
  width: 75%;
}

.col-md-12 {
  width: 100%;
}

/* 顶部信息栏样式 */
.top-bar {
  background-color: #1f73af;
  color: #fff;
  padding: 8px 0;
  width: 100%;
}

.contact-info span {
  margin-right: 15px;
  display: inline-block;
}

.social-links {
  text-align: right;
}

.social-links a {
  color: #fff;
  margin-left: 10px;
}

/* 主导航栏样式 */
.main-header {
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  padding: 15px 0;
  position: relative;
  z-index: 1000;
}

.main-header.fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

.logo img {
  max-height: 60px;
  display: block;
}

.main-nav {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

.main-nav ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.main-nav ul li {
  margin-left: 20px;
  position: relative;
}

.main-nav ul li a {
  color: #333;
  font-weight: 500;
  padding: 10px 0;
  text-decoration: none;
  display: block;
  transition: all 0.3s ease;
}

.main-nav ul li a:hover,
.main-nav ul li.active a {
  color: #1f73af;
}

.main-nav .has-dropdown .dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  padding: 10px 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1001;
}

.main-nav .has-dropdown:hover .dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.main-nav .dropdown li {
  margin: 0;
  display: block;
}

.main-nav .dropdown li a {
  padding: 8px 20px;
  display: block;
}

/* 轮播图样式 */
.hero-slider {
  position: relative;
}

.slider-item {
  position: relative;
  height: 500px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
}

.slider-item .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.slider-content {
  position: relative;
  z-index: 1;
  color: #fff;
  max-width: 700px;
  padding: 30px;
}

.slider-content h1 {
  font-size: 36px;
  margin-bottom: 20px;
}

.slider-content p {
  font-size: 18px;
  margin-bottom: 30px;
}

/* Bootstrap轮播样式 */
#heroCarousel {
    margin-bottom: 40px;
}

#heroCarousel .carousel-item {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
}

#heroCarousel .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

#heroCarousel .carousel-caption {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    bottom: auto;
    text-align: left;
    max-width: 600px;
    padding: 0;
}

#heroCarousel .carousel-caption h1 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #fff;
}

#heroCarousel .carousel-caption p {
    font-size: 18px;
    margin-bottom: 25px;
    color: #eee;
}

#heroCarousel .carousel-indicators {
    bottom: 30px;
}

#heroCarousel .carousel-indicators li {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
}

#heroCarousel .carousel-control-prev,
#heroCarousel .carousel-control-next {
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.7;
}

#heroCarousel .carousel-control-prev {
    left: 20px;
}

#heroCarousel .carousel-control-next {
    right: 20px;
}

#heroCarousel .carousel-control-prev:hover,
#heroCarousel .carousel-control-next:hover {
    opacity: 1;
}

@media (max-width: 768px) {
    #heroCarousel .carousel-item {
        height: 400px;
    }
    
    #heroCarousel .carousel-caption h1 {
        font-size: 32px;
    }
    
    #heroCarousel .carousel-caption p {
        font-size: 16px;
    }
}

/* 快速查询区样式 */
.quick-search {
  padding: 50px 0;
  background-color: #f8f8f8;
}

.search-box {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 15px rgba(0,0,0,0.1);
  padding: 30px;
  height: 100%;
  margin-bottom: 30px;
}

.search-box .icon-box {
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-radius: 50%;
  background-color: #1f73af;
  color: #fff;
  font-size: 24px;
  margin: 0 auto 20px;
}

.search-box h3 {
  text-align: center;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.search-form textarea.form-control,
.search-form input.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 15px;
  font-family: inherit;
}

.search-form textarea.form-control {
  height: 100px;
  resize: none;
}

.search-form .form-group {
  margin-bottom: 15px;
}

.btn-primary {
  background-color: #1f73af;
  color: #fff;
  padding: 10px 20px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
}

.btn-primary:hover {
  background-color: #186097;
}

/* 特色部分样式 */
.features-section {
  padding: 50px 0;
  background-color: #fff;
}

.section-title {
  text-align: center;
  margin-bottom: 40px;
}

.section-title h2 {
  font-size: 28px;
  margin-bottom: 10px;
  color: #333;
}

.section-title p {
  color: #777;
  font-size: 16px;
}

.feature-box {
  text-align: center;
  padding: 30px 20px;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  background-color: #fff;
  height: 100%;
}

.feature-box:hover {
  transform: translateY(-5px);
}

.feature-box .icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  border-radius: 50%;
  background-color: #1f73af;
  color: #fff;
  font-size: 30px;
  margin: 0 auto 20px;
}

.feature-box h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.feature-box p {
  color: #777;
  margin-bottom: 0;
}

/* 服务项目样式 */
.services-section {
  padding: 50px 0;
  background-color: #f8f8f8;
}

.service-card {
  text-align: center;
  padding: 30px;
  background: #fff;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin-bottom: 30px;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-icon {
  margin-bottom: 20px;
}

.service-icon img {
  max-width: 80px;
  height: auto;
}

.service-icon i {
  font-size: 48px;
  color: #1f73af;
  width: 80px;
  height: 80px;
  line-height: 80px;
  background: rgba(31, 115, 175, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.service-card:hover .service-icon i {
  color: #fff;
  background: #1f73af;
  transform: rotateY(360deg);
  transition: transform 0.8s;
}

.service-card h3 {
  font-size: 22px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #333;
}

.service-card p {
  color: #777;
  margin-bottom: 20px;
}

.read-more {
  color: #1f73af;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
}

.read-more:hover {
  color: #1251a3;
  text-decoration: none;
}

.read-more i {
  margin-left: 5px;
  transition: all 0.3s ease;
}

.read-more:hover i {
  margin-left: 10px;
}

/* 全球网络样式 */
.network-section {
  padding: 50px 0;
  background-color: #fff;
}

.network-map {
  margin-bottom: 30px;
}

.network-map img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0,0,0,0.1);
}

.region-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.region-item i {
  color: #1f73af;
  font-size: 20px;
  margin-right: 15px;
  margin-top: 3px;
}

.region-info h4 {
  font-size: 18px;
  margin-bottom: 5px;
  color: #333;
}

.region-info p {
  color: #777;
  margin-bottom: 0;
}

/* 页脚样式 */
.main-footer {
  background-color: #2c3e50;
  color: #fff;
  padding: 60px 0 0;
  box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
}

.footer-content {
  margin-bottom: 30px;
}

.footer-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 15px;
  color: #ecf0f1;
}

.footer-content h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: #3498db;
}

.footer-info p {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 20px;
}

.contact-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.contact-list li {
  padding: 10px 0;
  display: flex;
  align-items: flex-start;
  color: #bdc3c7;
}

.contact-list li i {
  color: #3498db;
  margin-right: 15px;
  font-size: 16px;
  width: 20px;
  text-align: center;
  margin-top: 4px;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  padding: 8px 0;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.footer-links ul li:last-child {
  border-bottom: none;
}

.footer-links ul li a {
  color: #bdc3c7;
  transition: all 0.3s ease;
  display: block;
  padding: 2px 0;
}

.footer-links ul li a:hover {
  color: #3498db;
  text-decoration: none;
  padding-left: 5px;
}

.footer-links ul li a:before {
  content: '\f105';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 10px;
  color: #3498db;
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-links ul li a:hover:before {
  opacity: 1;
}

.footer-qr {
  text-align: center;
}

.qr-code {
  background-color: #fff;
  padding: 10px;
  border-radius: 8px;
  display: inline-block;
  box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.qr-code img {
  max-width: 150px;
  height: auto;
  border-radius: 4px;
}

.qr-code p {
  color: #555;
  margin-top: 10px;
  margin-bottom: 0;
  font-size: 13px;
}

.footer-bottom {
  padding: 20px 0;
  margin-top: 20px;
  text-align: center;
  background-color: #1a2530;
  position: relative;
}

.copyright {
  color: #95a5a6;
  margin: 0;
  font-size: 14px;
}

.beian {
  margin-top: 8px;
}

.beian a {
  color: #95a5a6;
  font-size: 13px;
  transition: all 0.3s ease;
}

.beian a:hover {
  color: #3498db;
  text-decoration: none;
}

.social-icons {
  margin: 15px 0;
  display: flex;
  justify-content: center;
}

.social-icons a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255,255,255,0.1);
  color: #ecf0f1;
  border-radius: 50%;
  margin: 0 5px;
  transition: all 0.3s ease;
}

.social-icons a:hover {
  background-color: #3498db;
  color: #fff;
  transform: translateY(-3px);
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #1f73af;
  color: #fff;
  border-radius: 50%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #186097;
  color: #fff;
}

/* 内页样式 */
.page-header {
  padding: 50px 0;
  background-color: #1f73af;
  color: #fff;
  text-align: center;
  margin-bottom: 50px;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 18px;
  opacity: 0.8;
  margin-bottom: 0;
}

.breadcrumb {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 10px 20px;
  border-radius: 5px;
}

.breadcrumb a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.breadcrumb a:hover {
  color: #f0f8ff;
  text-decoration: underline;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.breadcrumb span {
  margin: 0 10px;
  color: #e0e0e0;
  font-weight: 500;
}

.content-section {
  padding: 50px 0;
}

.contact-map {
  height: 400px;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.contact-map iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.tracking-result {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0,0,0,0.1);
  padding: 30px;
  margin-bottom: 30px;
}

.tracking-result .result-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.tracking-result .result-info {
  margin-bottom: 20px;
}

.tracking-result .result-info p {
  margin-bottom: 5px;
}

.tracking-result .status-item {
  padding: 15px 0;
  border-bottom: 1px dashed #eee;
  display: flex;
}

.tracking-result .status-item:last-child {
  border-bottom: none;
}

.tracking-result .status-time {
  flex: 0 0 150px;
  font-weight: 500;
}

.tracking-result .status-info {
  flex: 1;
}

.service-detail {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0,0,0,0.1);
  padding: 30px;
  margin-bottom: 30px;
}

.service-detail h2 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.service-detail p {
  margin-bottom: 15px;
  line-height: 1.7;
}

.service-image {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: auto;
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

table th,
table td {
  padding: 12px 15px;
  text-align: left;
  border: 1px solid #ddd;
}

table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #333;
}

table tr:nth-child(even) {
  background-color: #f9f9f9;
}

table tr:hover {
  background-color: #f1f1f1;
}

/* 响应式样式 */
@media (max-width: 991px) {
  .slider-item {
    height: 400px;
  }
  
  .slider-content h1 {
    font-size: 28px;
  }
  
  .slider-content p {
    font-size: 16px;
  }
  
  .col-md-3,
  .col-md-4,
  .col-md-6,
  .col-md-8,
  .col-md-9,
  .col-md-12 {
    width: 100%;
  }
  
  .mobile-menu-toggle {
    display: block;
    float: right;
    background: none;
    border: none;
    font-size: 24px;
    color: #1f73af;
    cursor: pointer;
    padding: 5px;
  }
  
  .main-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    padding: 0;
    z-index: 1000;
  }
  
  .main-nav.show-mobile-menu {
    display: block;
  }
  
  .main-nav ul {
    display: block;
    padding: 10px 0;
  }
  
  .main-nav ul li {
    margin: 0;
    width: 100%;
    border-bottom: 1px solid #eee;
  }
  
  .main-nav ul li a {
    padding: 10px 20px;
    display: block;
  }
  
  .main-nav .has-dropdown .dropdown {
    position: static;
    width: 100%;
    box-shadow: none;
    opacity: 1;
    visibility: visible;
    transform: none;
    display: none;
    padding: 0;
  }
  
  .main-nav .has-dropdown:hover .dropdown {
    display: block;
  }
  
  .main-nav .dropdown li a {
    padding-left: 40px;
  }
  
  .social-links {
    text-align: left;
    margin-top: 10px;
  }
  
  .social-links a {
    margin-left: 0;
    margin-right: 10px;
  }
}

@media (max-width: 767px) {
  .slider-item {
    height: 300px;
  }
  
  .slider-content h1 {
    font-size: 24px;
  }
  
  .section-title h2 {
    font-size: 24px;
  }
  
  .page-header h1 {
    font-size: 28px;
  }
  
  .page-header p {
    font-size: 16px;
  }
  
  .footer-qr {
    text-align: left;
    margin-top: 30px;
  }
  
  .footer-qr h3:after {
    left: 0;
  }
  
  .qr-code img {
    margin-left: 0;
  }
}

@media (max-width: 575px) {
  .slider-item {
    height: 250px;
  }
  
  .slider-content {
    padding: 20px;
  }
  
  .slider-content h1 {
    font-size: 20px;
    margin-bottom: 10px;
  }
  
  .slider-content p {
    font-size: 14px;
    margin-bottom: 15px;
  }
}

/* 通用工具类 */
.mb-0 {
  margin-bottom: 0 !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

/* 动画效果 */
.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animate__fadeInDown {
  animation-name: fadeInDown;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -50px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 50px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* 修复transform和backface-visibility前缀问题 */
.animate-element {
  transform: translateY(20px);
  -webkit-transform: translateY(20px);
  -moz-transform: translateY(20px);
  -o-transform: translateY(20px);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
}

/* 修复display:inline-block和float冲突的问题 */
.sidebar-item {
  display: block;
  margin-bottom: 10px;
}

.sidebar-list li {
  display: block;
  margin-bottom: 5px;
}

/* 修复空规则集 */
.empty-rule {
  padding: 0;
  margin: 0;
}

/* 修复webkit-transform错误，使用正确的-webkit-transform */
.animated-element {
  transform: scale(1.05);
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  -o-transform: scale(1.05);
  transition: all 0.3s ease;
}

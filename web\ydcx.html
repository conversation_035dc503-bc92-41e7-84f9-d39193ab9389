<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运单查询 - 安徽东方国际物流有限公司 </title>
    <meta name="description" content="安徽东方国际物流有限公司 提供便捷的国际快递运单查询服务，实时跟踪您的包裹状态。">
    <meta name="keywords" content="运单查询,快递查询,物流跟踪,东方国际,国际快递">
    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="../css/unified-style.css">
    <!-- 网站图标 -->
    <link rel="icon" href="../images/favicon.ico" type="image/x-icon">
    <style>
        /* 添加自定义样式 */
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 15px;
        }
        
        .tracking-form {
          background-color: #fff;
          padding: 30px;
          border-radius: 8px;
          box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
          margin-bottom: 30px;
        }
        
        .tracking-form h2 {
          margin-bottom: 20px;
          color: #1f73af;
          font-weight: 600;
        }
        
        .form-control {
          height: 50px;
          border: 1px solid #ddd;
        }
        
        .btn-track {
          height: 50px;
          background-color: #1f73af;
          border-color: #1f73af;
          color: #fff;
          font-weight: 600;
          transition: all 0.3s ease;
        }
        
        .btn-track:hover {
          background-color: #1251a3;
          border-color: #1251a3;
        }
        
        .track-instructions {
          padding: 30px;
          background-color: #f8f9fa;
          border-radius: 8px;
          margin-bottom: 30px;
        }
        
        .track-instructions h3 {
          color: #1f73af;
          margin-bottom: 15px;
        }
        
        .track-steps {
          padding: 0;
          list-style-position: inside;
        }
        
        .track-steps li {
          margin-bottom: 10px;
          padding-left: 20px;
          position: relative;
        }
        
        .track-steps li:before {
          content: "";
          position: absolute;
          left: 0;
          top: 8px;
          width: 8px;
          height: 8px;
          background-color: #1f73af;
          border-radius: 50%;
        }
        
        /* 修复页脚样式 - 增强版 */
        html, body {
          width: 100%;
          overflow-x: hidden;
          margin: 0;
          padding: 0;
        }
        
        body {
          position: relative;
        }
        
        .main-footer {
          width: 100vw;
          margin: 0;
          padding: 50px 0 0;
          box-sizing: border-box;
          position: relative;
          left: 50%;
          right: 50%;
          margin-left: -50vw;
          margin-right: -50vw;
        }
        
        .footer-bottom {
          width: 100%;
          margin: 0;
          padding: 15px 0;
          box-sizing: border-box;
        }
        
        .container-fluid {
          width: 100%;
          margin: 0;
          padding: 0 15px;
          box-sizing: border-box;
          max-width: none;
        }
    </style>
</head>
<body>
    <!-- 顶部信息栏 -->
    <div class="top-bar">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="contact-info">
                        <span><i class="fas fa-phone-alt"></i> 业务热线：0551-63623651</span>
                        
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="social-links">
                        <a href="#" title="微信"><i class="fab fa-weixin"></i></a>
                        <a href="#" title="QQ"><i class="fab fa-qq"></i></a>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主导航栏 -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="logo">
                        <a href="../index.htm">
                            <img src="../images/logo.png" alt="安徽东方国际物流有限公司 ">
                        </a>
                    </div>
                </div>
                <div class="col-md-9">
                    <button class="mobile-menu-toggle d-md-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <nav class="main-nav">
                        <ul>
                            <li class="active"><a href="../index.htm">首页</a></li>
                            <li><a href="../web/ydcx.html">运单查询</a></li>
                            <li class="has-dropdown">
                                <a href="../web/express.html">产品服务</a>
                                <ul class="dropdown">
                                    <li><a href="../web/express.html">国际快递</a></li>
                                </ul>
                            </li>
                            <li><a href="../web/lianxi.html">联系我们</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <!-- 页面头部 -->
    <section class="page-header">
        <div class="container">
            <h1>运单查询</h1>
            <p>实时追踪您的包裹状态，掌握物流动态</p>
            <div class="breadcrumb">
                <a href="../index.htm">首页</a>
                <span>/</span>
                <a href="ydcx.html">运单查询</a>
            </div>
        </div>
    </section>

    <!-- 查询内容 -->
    <section class="content-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="search-box">
                        <div class="icon-box">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>快递运单查询</h3>
                        <p class="mb-20">请输入您要查询的运单号，支持国内外各大快递公司单号查询。</p>
                        <form class="search-form tracking-form needs-validation" novalidate id="trackingForm">
                            <div class="form-group">
                                <input type="text" class="form-control" name="tracking_no" placeholder="请输入运单号" required>
                                <div class="invalid-feedback">请输入有效的运单号</div>
                            </div>
                            <button type="submit" class="btn-primary">立即查询</button>
                        </form>
                    </div>

                    <!-- 查询结果，默认隐藏 -->
                    <div class="tracking-result" style="display: none;">
                        <!-- 结果内容将由JavaScript动态添加 -->
                    </div>

                   
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="footer-content footer-info">
                        <h3>关于我们</h3>
                        <p>安徽东方国际物流有限公司 是一家专业的国际物流服务提供商，致力于为客户提供安全、高效、可靠的国际物流解决方案。</p>
                        <ul class="contact-list">
                            <li><i class="fas fa-map-marker-alt"></i> 地址：合肥市蜀山区井岗路1295号蜀山里24幢101/102</li>
                            <li><i class="fas fa-phone"></i> 电话：0551-63623651</li>
                         
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-content footer-links">
                        <h3>快速链接</h3>
                        <ul>
                            <li><a href="../index.htm">公司首页</a></li>
                            <li><a href="ydcx.html">运单跟踪</a></li>
                            <li><a href="express.html">国际快递</a></li>
                            <li><a href="lianxi.html">联系我们</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <!-- 暂时隐藏二维码部分
                    <div class="footer-content footer-qr">
                        <h3>关注我们</h3>
                        <div class="qr-code">
                            <img src="../images/qr-code.jpg" alt="微信二维码">
                            <p>扫描二维码关注我们的微信公众号</p>
                        </div>
                    </div>
                    -->
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <p> 2025 安徽东方国际物流有限公司  版权所有</p>
                <div class="beian">
                    <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow">皖ICP备2022002479号-1</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮会由JS自动添加 -->

    <!-- 引入JS文件 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 运单查询表单提交
            $('#trackingForm').on('submit', function(e) {
                e.preventDefault(); // 阻止表单默认提交行为
                
                // 获取输入的运单号
                var trackingNumber = $('input[name="tracking_no"]').val();
                
                // 检查是否输入了运单号
                if (trackingNumber.trim() !== '') {
                    // 显示弹出框，提示运单号不存在
                    alert('运单号不存在，请确认单号');
                } else {
                    // 表单验证
                    this.classList.add('was-validated');
                }
            });

            // 移动菜单切换
            $('.mobile-menu-toggle').on('click', function() {
                $('.main-nav').toggleClass('show');
            });
        });
    </script>
</body>
</html>
/* Custom CSS for 安徽东方国际物流有限公司  */

/* Global styles */
body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  color: #333;
  line-height: 1.6;
}

/* Header improvements */
.mnmenu-sec {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hd-logo {
  padding: 10px 0;
}

/* Navigation menu */
.main-menu ul li a {
  font-weight: 500;
  transition: color 0.3s ease;
}

.main-menu ul li a:hover {
  color: #1f73af;
}

/* Slider improvements */
.slider-text h1 {
  font-size: 28px;
  font-weight: bold;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.slider-text p {
  font-size: 18px;
  margin-top: 15px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Function boxes */
.jpt_function {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.jpt_fctit {
  background: linear-gradient(to right, #1f73af, #2a8bc7);
}

.jpt_fcbut {
  background-color: #1f73af;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.jpt_fcbut:hover {
  background-color: #165b8c;
}

/* Service section */
.title h2 {
  color: #1f73af;
  font-weight: bold;
  position: relative;
  padding-bottom: 15px;
}

.title h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #1f73af;
}

/* Footer improvements */
#bottom {
  padding: 30px 0;
}

.footer-content p {
  margin: 10px 0;
}

.beian a {
  transition: opacity 0.3s ease;
}

.beian a:hover {
  opacity: 0.8;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .slider-text h1 {
    font-size: 22px;
  }
  
  .slider-text p {
    font-size: 16px;
  }
  
  .jpt_function {
    margin-bottom: 20px;
  }
}

/* 国际快递服务卡片样式 */
.service-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.service-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(31, 115, 175, 0.1);
  border-radius: 50%;
}

/* FAQ样式 */
.faq-item {
  border-left: 3px solid #1f73af;
  transition: transform 0.3s ease;
}

.faq-item:hover {
  transform: translateX(5px);
}

.faq-question {
  font-weight: bold;
  color: #1f73af;
}

/* 全球网络覆盖样式 */
.network-region {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px dashed #e0e0e0;
}

.network-region:last-child {
  border-bottom: none;
}

.region-icon {
  margin-right: 15px;
  color: #1f73af;
}

/* 联系方式样式改进 */
.contact-info-item {
  display: inline-flex;
  align-items: center;
  margin: 0 15px 10px;
  padding: 8px 15px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

.contact-icon {
  margin-right: 8px;
  color: #fff;
}

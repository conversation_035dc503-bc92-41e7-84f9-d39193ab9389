<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安徽东方国际物流有限公司 - 国际快递物流专家</title>
    <meta name="description" content="安徽东方国际物流有限公司专业提供国际空运、国际海运、国际快递、仓储配送等一站式物流服务。">
    <meta name="keywords" content="东方国际,国际货运,国际快递,国际物流,国际空运,国际海运,DHL,UPS,FedEx,TNT">
    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="../css/unified-style.css">
    <!-- 网站图标 -->
    <link rel="icon" href="../images/favicon.ico" type="image/x-icon">
    <style>
        .world-map-container {
            position: relative;
            width: 100%;
            height: 100%;
            background-color: #f0f0f0;
            border-radius: 50%;
        }

        .globe {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 100px;
            color: #337ab7;
        }

        .connection-points {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .point {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #337ab7;
        }

        .point-1 {
            top: 20%;
            left: 30%;
        }

        .point-2 {
            top: 50%;
            left: 70%;
        }

        .point-3 {
            top: 80%;
            left: 40%;
        }

        .point-4 {
            top: 30%;
            left: 60%;
        }

        .point-5 {
            top: 60%;
            left: 20%;
        }

        .line {
            position: absolute;
            width: 2px;
            height: 20px;
            background-color: #337ab7;
        }

        .line-1 {
            top: 25%;
            left: 35%;
            transform: rotate(45deg);
        }

        .line-2 {
            top: 55%;
            left: 75%;
            transform: rotate(135deg);
        }

        .line-3 {
            top: 85%;
            left: 45%;
            transform: rotate(225deg);
        }

        .line-4 {
            top: 35%;
            left: 65%;
            transform: rotate(315deg);
        }

        /* 自定义样式 */
        .service-image {
            text-align: center;
            margin-bottom: 15px;
        }

        .service-image i {
            font-size: 48px;
            color: #1f73af;
        }

        /* 修复页脚样式 - 增强版 */
        html,
        body {
            width: 100%;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        body {
            position: relative;
        }

        .main-footer {
            width: 100vw;
            margin: 0;
            padding: 50px 0 0;
            box-sizing: border-box;
            position: relative;
            left: 50%;
            right: 50%;
            margin-left: -50vw;
            margin-right: -50vw;
        }

        .footer-bottom {
            width: 100%;
            margin: 0;
            padding: 15px 0;
            box-sizing: border-box;
        }

        .container-fluid {
            width: 100%;
            margin: 0;
            padding: 0 15px;
            box-sizing: border-box;
            max-width: none;
        }

        /* 调整logo大小 */
        .logo img {
            max-height: 45px !important;
            width: auto;
        }
    </style>
</head>

<body>
    <!-- 顶部信息栏 -->
    <div class="top-bar">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="contact-info">
                        <span><i class="fas fa-phone-alt"></i> 业务热线：0551-63623651</span>
                        <!-- <span><i class="fas fa-envelope"></i>电子邮箱：<EMAIL></span> -->
                    </div>
                </div>
              
            </div>
        </div>
    </div>

    <!-- 主导航栏 -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="logo">
                        <a href="index.htm">
                            <img src="../images/logo.png" alt="安徽东方国际物流有限公司">
                        </a>
                    </div>
                </div>
                <div class="col-md-9">
                    <button class="mobile-menu-toggle d-md-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <nav class="main-nav">
                        <ul>
                            <li class="active"><a href="index.htm">首页</a></li>
                            <li><a href="web/ydcx.html">运单查询</a></li>
                            <li class="has-dropdown">
                                <a href="web/express.html">产品服务</a>
                                <ul class="dropdown">
                                    <li><a href="web/express.html">国际快递</a></li>
                                </ul>
                            </li>
                            <li><a href="web/lianxi.html">联系我们</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <!-- 首页轮播 - Bootstrap Carousel -->
    <div id="heroCarousel" class="carousel slide" data-ride="carousel">
        <ol class="carousel-indicators">
            <li data-target="#heroCarousel" data-slide-to="0" class="active"></li>
            <li data-target="#heroCarousel" data-slide-to="1"></li>
            <li data-target="#heroCarousel" data-slide-to="2"></li>
        </ol>
        <div class="carousel-inner">
            <div class="carousel-item active" style="background: linear-gradient(135deg, #1f73af 0%, #0c3b5a 100%);">
                <div class="overlay"></div>
                <div class="container">
                    <div class="carousel-caption text-left animate__animated animate__fadeInUp">
                        <h1>专业的国际物流解决方案</h1>
                        <p>为您提供全球一站式国际物流服务，让您的货物高效安全地抵达世界各地</p>
                        <a href="web/express.html" class="btn-primary">了解我们的服务</a>
                    </div>
                </div>
            </div>
            <div class="carousel-item" style="background: linear-gradient(135deg, #2c8ac3 0%, #16516e 100%);">
                <div class="overlay"></div>
                <div class="container">
                    <div class="carousel-caption text-left animate__animated animate__fadeInUp">
                        <h1>跨境电商物流专家</h1>
                        <p>为跨境电商提供专业的国际物流解决方案，助力您的全球业务发展</p>
                        <a href="web/express.html" class="btn-primary">了解我们的服务</a>
                    </div>
                </div>
            </div>
            <div class="carousel-item" style="background: linear-gradient(135deg, #3994cc 0%, #1c5b7d 100%);">
                <div class="overlay"></div>
                <div class="container">
                    <div class="carousel-caption text-left animate__animated animate__fadeInUp">
                        <h1>快速、安全、可靠</h1>
                        <p>我们与多家国际物流合作伙伴合作，为您提供安全可靠的国际快递服务</p>
                        <a href="web/express.html" class="btn-primary">了解我们的服务</a>
                    </div>
                </div>
            </div>
        </div>
        <a class="carousel-control-prev" href="#heroCarousel" role="button" data-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="sr-only">上一个</span>
        </a>
        <a class="carousel-control-next" href="#heroCarousel" role="button" data-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="sr-only">下一个</span>
        </a>
    </div>

    <!-- 快速查询区 -->
    <section class="quick-search">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="search-box">
                        <div class="icon-box">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>快递查询</h3>
                        <form class="search-form needs-validation" id="indexTrackingForm" novalidate>
                            <div class="form-group">
                                <input type="text" class="form-control" name="tracking_no" placeholder="请输入运单号"
                                    required>
                                <div class="invalid-feedback">请输入运单号</div>
                            </div>
                            <button type="submit" class="btn-primary">查询</button>
                        </form>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="search-box">
                        <div class="icon-box">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h3>运费查询</h3>
                        <form class="search-form needs-validation" novalidate>
                            <div class="form-group">
                                <select class="form-control" required>
                                    <option value="">- 选择发件国家 -</option>
                                    <option value="CN">中国</option>

                                </select>
                                <div class="invalid-feedback">请选择发件国家</div>
                            </div>
                            <div class="form-group">
                                <select class="form-control" required>
                                    <option value="">- 选择收件国家 -</option>
                                    <option value="CN">中国</option>
                                    <option value="US">美国</option>
                                    <option value="UK">英国</option>
                                    <option value="DE">德国</option>
                                    <option value="JP">日本</option>
                                </select>
                                <div class="invalid-feedback">请选择收件国家</div>
                            </div>
                            <div class="form-group">
                                <input type="number" class="form-control" placeholder="重量（KG）" required>
                                <div class="invalid-feedback">请输入重量</div>
                            </div>
                            <button type="submit" class="btn-primary">计算运费</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 我们的特色 -->
    <section class="features-section">
        <div class="container">
            <div class="section-title">
                <h2>我们的特色</h2>
                <p>以专业的态度，为客户提供优质的国际物流服务</p>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-box animate__animated" data-animation="animate__fadeInUp">
                        <div class="icon">
                            <i class="fas fa-truck-moving"></i>
                        </div>
                        <h3>全球门到门服务</h3>
                        <p>为客户提供全球门到门的一站式物流服务，无论您的货物需要发往何处，我们都能将其安全送达。</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-box animate__animated" data-animation="animate__fadeInUp">
                        <div class="icon">
                            <i class="fas fa-search-dollar"></i>
                        </div>
                        <h3>价格透明合理</h3>
                        <p>我们的价格体系透明合理，没有隐藏费用，确保客户能够清晰了解每一笔费用的构成。</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-box animate__animated" data-animation="animate__fadeInUp">
                        <div class="icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h3>专业客服团队</h3>
                        <p>我们拥有经验丰富的客服团队，提供专业、及时的咨询和问题解决服务，让您的物流无忧。</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-box animate__animated" data-animation="animate__fadeInUp">
                        <div class="icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>安全可靠保障</h3>
                        <p>我们与全球知名快递公司合作，确保您的货物在运输过程中得到全方位的安全保障。</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-box animate__animated" data-animation="animate__fadeInUp">
                        <div class="icon">
                            <i class="fas fa-globe-americas"></i>
                        </div>
                        <h3>全球网络覆盖</h3>
                        <p>我们的服务网络覆盖全球200多个国家和地区，为客户提供广泛的物流解决方案。</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-box animate__animated" data-animation="animate__fadeInUp">
                        <div class="icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3>一站式报关服务</h3>
                        <p>我们提供专业的报关服务，帮助客户处理复杂的国际贸易文件和手续，让跨境物流更加便捷。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 服务项目 -->
    <section class="services-section">
        <div class="container">
            <div class="section-title">
                <h2>我们的服务</h2>
                <p>为您提供全方位的国际物流解决方案</p>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="service-card animate__animated" data-animation="animate__fadeInUp">
                        <div class="service-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h3>国际快递</h3>
                        <p>提供安全快速的国际快递服务，高效可靠地将您的包裹送达全球各地。</p>
                        <a href="web/express.html" class="read-more">了解更多 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="service-card animate__animated" data-animation="animate__fadeInUp">
                        <div class="service-icon">
                            <i class="fas fa-plane"></i>
                        </div>
                        <h3>国际空运</h3>
                        <p>提供全球空运服务，为客户的货物提供快速、高效的国际空运解决方案。</p>
                        <a href="web/express.html" class="read-more">了解更多 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="service-card animate__animated" data-animation="animate__fadeInUp">
                        <div class="service-icon">
                            <i class="fas fa-ship"></i>
                        </div>
                        <h3>国际海运</h3>
                        <p>提供FCL、LCL等多种海运服务，满足客户不同货量、不同成本的国际海运需求。</p>
                        <a href="web/express.html" class="read-more">了解更多 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="service-card animate__animated" data-animation="animate__fadeInUp">
                        <div class="service-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <h3>仓储配送</h3>
                        <p>提供专业的仓储管理和配送服务，帮助客户降低物流成本，提高配送效率。</p>
                        <a href="web/express.html" class="read-more">了解更多 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 全球网络 -->
    <section class="network-section">
        <div class="container">
            <div class="section-title">
                <h2>全球网络</h2>
                <p>我们的服务网络覆盖全球多个国家和地区</p>
            </div>
            <div class="row">

                <div class="col-md-6">
                    <div class="region-list animate__animated" data-animation="animate__fadeInRight">
                    
                        <div class="region-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div class="region-info">
                                <h4>欧洲</h4>
                                <p>中国寄达奥地利、比利时、法国、德国、爱尔兰、意大利、摩纳哥、荷兰、西班牙、瑞典、瑞士、英国、卢森堡、丹麦、挪威的国际快递（邮政企业专营业务除外）</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-4">
                    <div class="footer-content footer-info">
                        <h3>关于我们</h3>
                        <p>安徽东方国际物流有限公司是一家专业的国际物流服务提供商，致力于为客户提供安全、高效、可靠的国际物流解决方案。</p>
                        <ul class="contact-list">
                            <li><i class="fas fa-map-marker-alt"></i> 地址：合肥市蜀山区井岗路1295号蜀山里24幢101/102</li>
                            <li><i class="fas fa-phone"></i> 电话：0551-63623651</li>
                           
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-content footer-links">
                        <h3>快速链接</h3>
                        <ul>
                            <li><a href="index.htm">公司首页</a></li>
                            <li><a href="web/ydcx.html">运单跟踪</a></li>
                            <li><a href="web/express.html">国际快递</a></li>
                            <li><a href="web/lianxi.html">联系我们</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <!-- 暂时隐藏二维码部分
                    <div class="footer-content footer-qr">
                        <h3>关注我们</h3>
                        <div class="qr-code">
                            <img src="images/qr-code.jpg" alt="微信二维码">
                            <p>扫描二维码关注我们的微信公众号</p>
                        </div>
                    </div>
                    -->
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <p> 2025 安徽东方国际物流有限公司 版权所有 | 网站：www.ahoia.com.cn</p>
                <div class="beian">
                    <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow">皖ICP备2022002479号-1</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮会由JS自动添加 -->

    <!-- 引入JS文件 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css"></script>
    <script>
        $(document).ready(function () {
            // 运单查询表单提交（首页）
            $('#indexTrackingForm').on('submit', function (e) {
                e.preventDefault(); // 阻止表单默认提交行为

                // 获取输入的运单号
                var trackingNumber = $(this).find('input[name="tracking_no"]').val();

                // 检查是否输入了运单号
                if (trackingNumber.trim() !== '') {
                    // 显示弹出框，提示运单号不存在
                    alert('运单号不存在，请确认单号');
                } else {
                    // 表单验证
                    this.classList.add('was-validated');
                }
            });

            // 运费查询表单提交处理
            $('.search-form').not('#indexTrackingForm').on('submit', function (e) {
                e.preventDefault();

                // 表单验证
                if (!this.checkValidity()) {
                    this.classList.add('was-validated');
                    return;
                }

                // 这里可以添加实际的运费查询逻辑
                alert('正在建设中，敬请期待！');
            });

            // 为带有data-animation属性的元素添加滚动监听和动画
            var animatedElements = $('[data-animation]');

            // 检查元素是否在视口中
            function checkInView() {
                var windowHeight = $(window).height();
                var windowTopPosition = $(window).scrollTop();
                var windowBottomPosition = (windowTopPosition + windowHeight);

                $.each(animatedElements, function () {
                    var element = $(this);
                    var elementHeight = element.outerHeight();
                    var elementTopPosition = element.offset().top;
                    var elementBottomPosition = (elementTopPosition + elementHeight);

                    // 检查元素是否在视口中
                    if ((elementBottomPosition >= windowTopPosition) &&
                        (elementTopPosition <= windowBottomPosition)) {
                        element.addClass(element.data('animation'));
                    }
                });
            }

            // 页面加载和滚动时检查
            $(window).on('scroll resize', checkInView);
            $(window).trigger('scroll');

            // 移动菜单切换
            $('.mobile-menu-toggle').on('click', function () {
                $('.main-nav').toggleClass('show');
            });
        });
    </script>
</body>
</html>
/**
 * 安徽东方国际物流有限公司  统一JavaScript功能
 * 包含导航、表单验证、返回顶部和其他交互功能
 */

(function() {
    'use strict';

    // DOM元素加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化各功能
        initMobileMenu();
        initScrollEvents();
        initBackToTop();
        initFormValidation();
        initTrackingForm();
        initOwlCarousel();
    });

    /**
     * 移动端菜单初始化
     */
    function initMobileMenu() {
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const mainNav = document.querySelector('.main-nav');
        
        if (mobileToggle && mainNav) {
            mobileToggle.addEventListener('click', function() {
                mainNav.classList.toggle('show-mobile-menu');
                this.classList.toggle('active');
            });

            // 点击下拉菜单切换显示
            const dropdownLinks = document.querySelectorAll('.has-dropdown > a');
            dropdownLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    if (window.innerWidth < 992) {
                        e.preventDefault();
                        const parent = this.parentNode;
                        const dropdown = parent.querySelector('.dropdown');
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // 窗口大小改变时处理菜单状态
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 992) {
                    mainNav.classList.remove('show-mobile-menu');
                    mobileToggle.classList.remove('active');
                    document.querySelectorAll('.dropdown').forEach(function(dropdown) {
                        dropdown.style.display = '';
                    });
                }
            });
        }
    }

    /**
     * 滚动事件处理
     */
    function initScrollEvents() {
        const header = document.querySelector('.main-header');
        let headerHeight = header ? header.offsetHeight : 0;
        let lastScrollTop = 0;

        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // 处理固定导航栏
            if (header) {
                if (scrollTop > headerHeight && scrollTop > lastScrollTop) {
                    // 向下滚动超过导航栏高度
                    header.classList.remove('fixed');
                } else if (scrollTop > headerHeight) {
                    // 向上滚动且超过导航栏高度
                    header.classList.add('fixed');
                } else {
                    // 回到顶部
                    header.classList.remove('fixed');
                }
            }

            lastScrollTop = scrollTop;
            
            // 处理返回顶部按钮显示
            toggleBackToTopButton(scrollTop);
            
            // 处理动画元素显示
            animateOnScroll();
        });
    }

    /**
     * 返回顶部按钮功能
     */
    function initBackToTop() {
        // 创建返回顶部按钮
        const backToTopBtn = document.createElement('a');
        backToTopBtn.href = '#';
        backToTopBtn.className = 'back-to-top';
        backToTopBtn.innerHTML = '<i class="fa fa-angle-up"></i>';
        document.body.appendChild(backToTopBtn);
        
        // 点击事件
        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            scrollToTop();
        });
    }

    /**
     * 显示/隐藏返回顶部按钮
     */
    function toggleBackToTopButton(scrollTop) {
        const backToTopBtn = document.querySelector('.back-to-top');
        if (backToTopBtn) {
            if (scrollTop > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        }
    }

    /**
     * 平滑滚动到顶部
     */
    function scrollToTop() {
        const scrollDuration = 500;
        const scrollStep = -window.scrollY / (scrollDuration / 15);
        
        const scrollInterval = setInterval(function() {
            if (window.scrollY !== 0) {
                window.scrollBy(0, scrollStep);
            } else {
                clearInterval(scrollInterval);
            }
        }, 15);
    }

    /**
     * 在滚动时添加动画效果
     */
    function animateOnScroll() {
        const elements = document.querySelectorAll('.animate__animated:not(.animate__triggered)');
        
        elements.forEach(function(element) {
            if (isElementInViewport(element)) {
                const animationClass = element.getAttribute('data-animation') || 'animate__fadeInUp';
                element.classList.add(animationClass, 'animate__triggered');
            }
        });
    }

    /**
     * 判断元素是否在可视范围内
     */
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.9 &&
            rect.bottom >= 0 &&
            rect.left <= (window.innerWidth || document.documentElement.clientWidth) &&
            rect.right >= 0
        );
    }

    /**
     * 表单验证初始化
     */
    function initFormValidation() {
        const forms = document.querySelectorAll('form.needs-validation');
        
        forms.forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!validateForm(form)) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }

    /**
     * 表单验证
     */
    function validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        
        inputs.forEach(function(input) {
            if (!input.value.trim()) {
                isValid = false;
                showError(input, '此字段不能为空');
            } else if (input.type === 'email' && !validateEmail(input.value)) {
                isValid = false;
                showError(input, '请输入有效的电子邮件地址');
            } else if (input.type === 'tel' && !validatePhone(input.value)) {
                isValid = false;
                showError(input, '请输入有效的电话号码');
            } else {
                clearError(input);
            }
        });
        
        return isValid;
    }

    /**
     * 显示错误信息
     */
    function showError(input, message) {
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            input.classList.add('is-invalid');
            
            let errorElement = formGroup.querySelector('.invalid-feedback');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'invalid-feedback';
                formGroup.appendChild(errorElement);
            }
            
            errorElement.textContent = message;
        }
    }

    /**
     * 清除错误信息
     */
    function clearError(input) {
        input.classList.remove('is-invalid');
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            const errorElement = formGroup.querySelector('.invalid-feedback');
            if (errorElement) {
                errorElement.textContent = '';
            }
        }
    }

    /**
     * 验证邮箱格式
     */
    function validateEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    /**
     * 验证电话号码格式
     */
    function validatePhone(phone) {
        const re = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
        return re.test(String(phone));
    }

    /**
     * 初始化运单查询表单
     */
    function initTrackingForm() {
        const trackingForm = document.querySelector('.tracking-form');
        const trackingResult = document.querySelector('.tracking-result');
        
        if (trackingForm) {
            trackingForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const trackingNumber = this.querySelector('input[name="tracking_no"]').value;
                if (!trackingNumber.trim()) {
                    showError(this.querySelector('input[name="tracking_no"]'), '请输入运单号');
                    return;
                }
                
                // 这里应该是真实的查询请求，现在只是模拟
                simulateTrackingResult(trackingNumber);
            });
        }
    }

    /**
     * 模拟查询结果
     */
    function simulateTrackingResult(trackingNumber) {
       
        
    }

    /**
     * 初始化Owl Carousel轮播
     * 注意：需要先加载Owl Carousel库
     */
    function initOwlCarousel() {
        if (typeof $.fn.owlCarousel !== 'undefined') {
            // 初始化首页轮播
            $('.hero-slider').owlCarousel({
                items: 1,
                loop: true,
                margin: 0,
                nav: true,
                dots: true,
                autoplay: true,
                autoplayTimeout: 5000,
                autoplayHoverPause: true,
                navText: ['<i class="fa fa-angle-left"></i>', '<i class="fa fa-angle-right"></i>'],
                responsive: {
                    0: {
                        nav: false
                    },
                    768: {
                        nav: true
                    }
                }
            });
        }
    }

})();
